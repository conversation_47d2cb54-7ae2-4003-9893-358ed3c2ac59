.summary-dashboard-container {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;
  text-align: center;
}

.dashboard-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.dashboard-header p {
  color: #7f8c8d;
  font-size: 0.95rem;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.stat-card.total {
  border-left: 4px solid #3498db;
}

.stat-card.errors {
  border-left: 4px solid #e74c3c;
}

.stat-card.errors.critical {
  background: #fdf2f2;
  border-left-color: #c0392b;
}

.stat-card.errors.warning {
  background: #fef9e7;
  border-left-color: #f39c12;
}

.stat-card.warnings {
  border-left: 4px solid #f39c12;
}

.stat-card.info {
  border-left: 4px solid #3498db;
}

.stat-card.debug {
  border-left: 4px solid #95a5a6;
}

.stat-card.fatal {
  border-left: 4px solid #8e44ad;
}

.time-range-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
  text-align: center;
}

.time-range-card h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.time-range-card p {
  margin: 0;
  color: #7f8c8d;
  font-size: 1rem;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-card h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.hourly-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 200px;
  padding: 1rem 0;
}

.hourly-bar {
  flex: 1;
  background: #ecf0f1;
  border-radius: 2px;
  position: relative;
  min-height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  transition: background 0.2s ease;
}

.hourly-bar:hover {
  background: #3498db !important;
}

.hour-label {
  font-size: 0.7rem;
  color: #7f8c8d;
  margin-bottom: 0.25rem;
  text-align: center;
}

.hour-count {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.sources-list,
.errors-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.source-item,
.error-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.source-info,
.error-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.source-name,
.error-message {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.source-count,
.error-count {
  color: #7f8c8d;
  font-size: 0.8rem;
}

.source-bar,
.error-bar {
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.source-fill,
.error-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.error-fill {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.empty-state p {
  font-size: 0.95rem;
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-icon {
    font-size: 1.5rem;
  }
  
  .stat-content h3 {
    font-size: 1.2rem;
  }
  
  .hourly-chart {
    height: 150px;
  }
  
  .hour-label {
    font-size: 0.6rem;
  }
  
  .hour-count {
    font-size: 0.7rem;
  }
} 