import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { OpenAIService } from '../../../core/services/openai.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-api-key-config',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './api-key-config.component.html',
  styleUrls: ['./api-key-config.component.css']
})
export class ApiKeyConfigComponent implements OnInit {
  apiKey = '';
  isConfigured = false;
  showKey = false;
  isSaving = false;
  message = '';
  messageType: 'success' | 'error' | 'info' = 'info';

  constructor(private openaiService: OpenAIService) { }

  ngOnInit(): void {
    this.apiKey = environment.openai.apiKey || '';
    this.updateStatus();
  }

  updateStatus(): void {
    this.isConfigured = this.openaiService.isConfigured();
  }

  toggleShowKey(): void {
    this.showKey = !this.showKey;
  }

  async saveApiKey(): Promise<void> {
    if (!this.apiKey.trim()) {
      this.showMessage('Please enter a valid API key', 'error');
      return;
    }

    this.isSaving = true;
    this.message = '';

    try {
      (environment as any).openai.apiKey = this.apiKey;
      this.updateStatus();
      this.showMessage('API key saved successfully!', 'success');
    } catch (error) {
      this.showMessage('Failed to save API key: ' + (error as Error).message, 'error');
    } finally {
      this.isSaving = false;
    }
  }

  clearApiKey(): void {
    this.apiKey = '';
    (environment as any).openai.apiKey = '';
    this.updateStatus();
    this.showMessage('API key cleared', 'info');
  }

  private showMessage(text: string, type: 'success' | 'error' | 'info'): void {
    this.message = text;
    this.messageType = type;
    setTimeout(() => {
      this.message = '';
    }, 5000);
  }

  getStatusIcon(): string {
    return this.isConfigured ? '✅' : '❌';
  }

  getStatusText(): string {
    return this.isConfigured ? 'Configured' : 'Not Configured';
  }
} 