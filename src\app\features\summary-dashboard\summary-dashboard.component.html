<div class="summary-dashboard-container">
  <div class="dashboard-header">
    <h2>Log Analysis Summary</h2>
    <p *ngIf="statistics">File: {{ statistics.fileName }} | Processing Time: {{ formatProcessingTime() }} | Size: {{ formatFileSize() }}</p>
  </div>

  <!-- Overview Cards -->
  <div class="overview-cards" *ngIf="statistics">
    <div class="stat-card total">
      <div class="stat-icon">📊</div>
      <div class="stat-content">
        <h3>{{ logEntries.length }}</h3>
        <p>Total Entries</p>
      </div>
    </div>

    <div class="stat-card errors" [class]="getLevelClass('ERROR')">
      <div class="stat-icon">🔴</div>
      <div class="stat-content">
        <h3>{{ getLevelCount('ERROR') }}</h3>
        <p>Errors ({{ getLevelPercentage('ERROR').toFixed(1) }}%)</p>
      </div>
    </div>

    <div class="stat-card warnings">
      <div class="stat-icon">🟡</div>
      <div class="stat-content">
        <h3>{{ getLevelCount('WARN') }}</h3>
        <p>Warnings ({{ getLevelPercentage('WARN').toFixed(1) }}%)</p>
      </div>
    </div>

    <div class="stat-card info">
      <div class="stat-icon">🔵</div>
      <div class="stat-content">
        <h3>{{ getLevelCount('INFO') }}</h3>
        <p>Info Messages</p>
      </div>
    </div>

    <div class="stat-card debug">
      <div class="stat-icon">⚪</div>
      <div class="stat-content">
        <h3>{{ getLevelCount('DEBUG') }}</h3>
        <p>Debug Messages</p>
      </div>
    </div>

    <div class="stat-card fatal">
      <div class="stat-icon">💀</div>
      <div class="stat-content">
        <h3>{{ getLevelCount('FATAL') }}</h3>
        <p>Fatal Errors</p>
      </div>
    </div>
  </div>

  <!-- Time Range -->
  <div class="time-range-card" *ngIf="statistics">
    <h3>Time Range</h3>
    <p>{{ formatTimeRange() }}</p>
  </div>

  <!-- Charts Section -->
  <div class="charts-section" *ngIf="statistics">
    <!-- Hourly Distribution -->
    <div class="chart-card">
      <h3>Hourly Distribution</h3>
      <div class="hourly-chart">
        <div class="hourly-bar" 
             *ngFor="let hour of getHourlyDistribution()"
             [style.height.%]="getHourlyPercentage(hour.count)"
             [style.background-color]="hour.count > 0 ? '#3498db' : '#ecf0f1'">
          <span class="hour-label">{{ getHourLabel(hour.hour) }}</span>
          <span class="hour-count">{{ hour.count }}</span>
        </div>
      </div>
    </div>

    <!-- Top Sources -->
    <div class="chart-card">
      <h3>Top Sources</h3>
      <div class="sources-list">
        <div class="source-item" *ngFor="let source of getTopSources()">
          <div class="source-info">
            <span class="source-name">{{ source.source }}</span>
            <span class="source-count">{{ source.count }} entries</span>
          </div>
          <div class="source-bar">
            <div class="source-fill" [style.width.%]="(source.count / logEntries.length) * 100"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Errors -->
    <div class="chart-card">
      <h3>Top Errors</h3>
      <div class="errors-list">
        <div class="error-item" *ngFor="let error of getTopErrors()">
          <div class="error-info">
            <span class="error-message">{{ error.message.substring(0, 80) }}{{ error.message.length > 80 ? '...' : '' }}</span>
            <span class="error-count">{{ error.count }} occurrences</span>
          </div>
          <div class="error-bar">
            <div class="error-fill" [style.width.%]="(error.count / logEntries.length) * 100"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!statistics">
    <div class="empty-icon">📈</div>
    <h3>No Data Available</h3>
    <p>Upload a log file to see the analysis summary.</p>
  </div>
</div> 