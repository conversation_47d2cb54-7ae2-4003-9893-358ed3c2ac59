import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { LogEntry } from './core/models/log-entry.model';
import { LogStatistics } from './core/models/log-summary.model';
import { AppStateService } from './core/services/app-state.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'AI Log Analyzer';
  
  // Shared state for log data
  logEntries: LogEntry[] = [];
  logStatistics: LogStatistics | null = null;

  private stateSubscription: Subscription | null = null;

  constructor(private appStateService: AppStateService) { }

  ngOnInit(): void {
    // Subscribe to state changes
    this.stateSubscription = this.appStateService.state$.subscribe(state => {
      this.logEntries = state.logEntries;
      this.logStatistics = state.logStatistics;
    });

    // Get initial state
    this.logEntries = this.appStateService.getLogEntries();
    this.logStatistics = this.appStateService.getLogStatistics();
  }

  ngOnDestroy(): void {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  hasLogData(): boolean {
    return this.appStateService.hasLogData();
  }

  getLogCount(): number {
    return this.appStateService.getLogCount();
  }

  getErrorCount(): number {
    return this.appStateService.getErrorCount();
  }
}
