<div class="log-viewer-container">
  <div class="viewer-header">
    <h2>Log Viewer</h2>
    <p>{{ getFilterSummary() }}</p>
  </div>

  <!-- Filters -->
  <div class="filters-section">
    <div class="filter-group">
      <label>Log Levels:</label>
      <div class="level-filters">
        <button 
          *ngFor="let level of logLevels" 
          [class]="'level-btn ' + getLevelClass(level)"
          [class.selected]="selectedLevels.includes(level)"
          (click)="onLevelChange(level)">
          {{ getLevelIcon(level) }} {{ level }}
        </button>
      </div>
    </div>

    <div class="filter-group">
      <label for="search-input">Search:</label>
      <input 
        id="search-input"
        type="text" 
        [(ngModel)]="searchTerm"
        (input)="onSearchChange()"
        placeholder="Search in messages and sources...">
    </div>

    <div class="filter-group">
      <label for="start-date">Start Date:</label>
      <input 
        id="start-date"
        type="date" 
        [(ngModel)]="startDate"
        (change)="onDateChange()">
    </div>

    <div class="filter-group">
      <label for="end-date">End Date:</label>
      <input 
        id="end-date"
        type="date" 
        [(ngModel)]="endDate"
        (change)="onDateChange()">
    </div>

    <button class="clear-filters-btn" (click)="clearFilters()">
      Clear Filters
    </button>
  </div>

  <!-- Log Table -->
  <div class="log-table-container">
    <table class="log-table">
      <thead>
        <tr>
          <th (click)="onSortChange('timestamp')" class="sortable">
            Timestamp
            <span *ngIf="sortBy === 'timestamp'" class="sort-indicator">
              {{ sortOrder === 'asc' ? '↑' : '↓' }}
            </span>
          </th>
          <th (click)="onSortChange('level')" class="sortable">
            Level
            <span *ngIf="sortBy === 'level'" class="sort-indicator">
              {{ sortOrder === 'asc' ? '↑' : '↓' }}
            </span>
          </th>
          <th (click)="onSortChange('message')" class="sortable">
            Message
            <span *ngIf="sortBy === 'message'" class="sort-indicator">
              {{ sortOrder === 'asc' ? '↑' : '↓' }}
            </span>
          </th>
          <th>Source</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let entry of getCurrentPageEntries()" [class]="getLevelClass(entry.level)">
          <td class="timestamp">{{ formatTimestamp(entry.timestamp) }}</td>
          <td class="level">
            <span class="level-badge">
              {{ getLevelIcon(entry.level) }} {{ entry.level }}
            </span>
          </td>
          <td class="message">{{ entry.message }}</td>
          <td class="source">{{ entry.source || '-' }}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="totalPages > 1">
    <div class="pagination-info">
      Page {{ currentPage }} of {{ totalPages }}
    </div>
    
    <div class="pagination-controls">
      <button 
        class="page-btn" 
        [disabled]="currentPage === 1"
        (click)="goToPage(currentPage - 1)">
        Previous
      </button>
      
      <button 
        *ngFor="let page of getPageNumbers()"
        class="page-btn"
        [class.active]="page === currentPage"
        (click)="goToPage(page)">
        {{ page }}
      </button>
      
      <button 
        class="page-btn" 
        [disabled]="currentPage === totalPages"
        (click)="goToPage(currentPage + 1)">
        Next
      </button>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="filteredEntries.length === 0">
    <div class="empty-icon">📋</div>
    <h3>No log entries found</h3>
    <p>Try adjusting your filters or upload a log file.</p>
  </div>
</div> 