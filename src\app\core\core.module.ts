import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LogParserService } from './services/log-parser.service';
import { SummaryGeneratorService } from './services/summary-generator.service';
import { AIServiceService } from './services/ai-service.service';
import { OpenAIService } from './services/openai.service';
import { AppStateService } from './services/app-state.service';

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ],
  providers: [
    LogParserService,
    SummaryGeneratorService,
    AIServiceService,
    OpenAIService,
    AppStateService
  ]
})
export class CoreModule { } 