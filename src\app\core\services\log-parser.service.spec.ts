import { TestBed } from '@angular/core/testing';
import { LogParserService } from './log-parser.service';
import { LogEntry } from '../models/log-entry.model';

describe('LogParserService', () => {
  let service: LogParserService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(LogParserService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('parseLogLevel', () => {
    it('should parse standard log levels correctly', () => {
      // Access private method for testing
      const parseLogLevel = (service as any).parseLogLevel.bind(service);
      
      expect(parseLogLevel('INFO')).toBe('INFO');
      expect(parseLogLevel('WARN')).toBe('WARN');
      expect(parseLogLevel('ERROR')).toBe('ERROR');
      expect(parseLogLevel('DEBUG')).toBe('DEBUG');
      expect(parseLogLevel('FATAL')).toBe('FATAL');
    });

    it('should handle log level aliases', () => {
      const parseLogLevel = (service as any).parseLogLevel.bind(service);
      
      // ERROR aliases
      expect(parseLogLevel('ERR')).toBe('ERROR');
      expect(parseLogLevel('SEVERE')).toBe('ERROR');
      
      // WARN aliases
      expect(parseLogLevel('WARNING')).toBe('WARN');
      expect(parseLogLevel('CAUTION')).toBe('WARN');
      
      // INFO aliases
      expect(parseLogLevel('INFORMATION')).toBe('INFO');
      expect(parseLogLevel('NOTICE')).toBe('INFO');
      
      // DEBUG aliases
      expect(parseLogLevel('DBG')).toBe('DEBUG');
      expect(parseLogLevel('TRACE')).toBe('DEBUG');
      expect(parseLogLevel('VERBOSE')).toBe('DEBUG');
      
      // FATAL aliases
      expect(parseLogLevel('CRITICAL')).toBe('FATAL');
      expect(parseLogLevel('CRIT')).toBe('FATAL');
      expect(parseLogLevel('EMERGENCY')).toBe('FATAL');
      expect(parseLogLevel('EMERG')).toBe('FATAL');
    });

    it('should handle case insensitive input', () => {
      const parseLogLevel = (service as any).parseLogLevel.bind(service);
      
      expect(parseLogLevel('info')).toBe('INFO');
      expect(parseLogLevel('Error')).toBe('ERROR');
      expect(parseLogLevel('WARN')).toBe('WARN');
      expect(parseLogLevel('debug')).toBe('DEBUG');
      expect(parseLogLevel('Fatal')).toBe('FATAL');
    });

    it('should default to INFO for unknown levels', () => {
      const parseLogLevel = (service as any).parseLogLevel.bind(service);
      
      expect(parseLogLevel('UNKNOWN')).toBe('INFO');
      expect(parseLogLevel('CUSTOM')).toBe('INFO');
      expect(parseLogLevel('')).toBe('INFO');
    });
  });

  describe('parseLogLine', () => {
    it('should parse standard log format', () => {
      const parseLogLine = (service as any).parseLogLine.bind(service);
      
      const result = parseLogLine('2024-01-15 10:30:15 INFO Application started successfully', '1');
      
      expect(result).toBeTruthy();
      expect(result.level).toBe('INFO');
      expect(result.message).toBe('Application started successfully');
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should parse all log levels from standard format', () => {
      const parseLogLine = (service as any).parseLogLine.bind(service);
      
      const testCases = [
        { line: '2024-01-15 10:30:15 INFO Test message', expectedLevel: 'INFO' },
        { line: '2024-01-15 10:30:15 WARN Test warning', expectedLevel: 'WARN' },
        { line: '2024-01-15 10:30:15 ERROR Test error', expectedLevel: 'ERROR' },
        { line: '2024-01-15 10:30:15 DEBUG Test debug', expectedLevel: 'DEBUG' },
        { line: '2024-01-15 10:30:15 FATAL Test fatal', expectedLevel: 'FATAL' }
      ];

      testCases.forEach((testCase, index) => {
        const result = parseLogLine(testCase.line, index.toString());
        expect(result).toBeTruthy();
        expect(result.level).toBe(testCase.expectedLevel);
      });
    });

    it('should parse bracketed log format', () => {
      const parseLogLine = (service as any).parseLogLine.bind(service);
      
      const result = parseLogLine('2024-01-15 10:30:15 [ERROR] Database connection failed', '1');
      
      expect(result).toBeTruthy();
      expect(result.level).toBe('ERROR');
      expect(result.message).toBe('Database connection failed');
    });

    it('should parse level-first format', () => {
      const parseLogLine = (service as any).parseLogLine.bind(service);
      
      const result = parseLogLine('WARN 2024-01-15 10:30:15 High memory usage', '1');
      
      expect(result).toBeTruthy();
      expect(result.level).toBe('WARN');
      expect(result.message).toBe('High memory usage');
    });

    it('should parse simple level format', () => {
      const parseLogLine = (service as any).parseLogLine.bind(service);
      
      const result = parseLogLine('ERROR: Connection timeout', '1');
      
      expect(result).toBeTruthy();
      expect(result.level).toBe('ERROR');
      expect(result.message).toBe('Connection timeout');
    });

    it('should handle format with thread/source', () => {
      const parseLogLine = (service as any).parseLogLine.bind(service);
      
      const result = parseLogLine('2024-01-15 10:30:15 [INFO] [MainThread] Application started', '1');
      
      expect(result).toBeTruthy();
      expect(result.level).toBe('INFO');
      expect(result.message).toBe('Application started');
      expect(result.source).toBe('MainThread');
    });

    it('should fallback to INFO for unrecognized format', () => {
      const parseLogLine = (service as any).parseLogLine.bind(service);
      
      const result = parseLogLine('Some random log message without format', '1');
      
      expect(result).toBeTruthy();
      expect(result.level).toBe('INFO');
      expect(result.message).toBe('Some random log message without format');
    });
  });

  describe('parseLogFile', () => {
    it('should parse a file with all log levels', async () => {
      const logContent = `2024-01-15 10:30:15 INFO Application started successfully
2024-01-15 10:30:16 DEBUG Database connection established
2024-01-15 10:30:17 WARN High memory usage detected: 85%
2024-01-15 10:30:18 ERROR Database query timeout after 30 seconds
2024-01-15 10:30:19 FATAL Critical system error: Unable to start services`;

      const file = new File([logContent], 'test.log', { type: 'text/plain' });
      
      const entries = await service.parseLogFile(file);
      
      expect(entries.length).toBe(5);
      expect(entries[0].level).toBe('INFO');
      expect(entries[1].level).toBe('DEBUG');
      expect(entries[2].level).toBe('WARN');
      expect(entries[3].level).toBe('ERROR');
      expect(entries[4].level).toBe('FATAL');
    });

    it('should handle mixed log formats', async () => {
      const logContent = `2024-01-15 10:30:15 INFO Standard format
2024-01-15 10:30:16 [DEBUG] Bracketed format
WARN 2024-01-15 10:30:17 Level first format
ERROR: Simple format
2024-01-15 10:30:18 [FATAL] [Thread1] With source format`;

      const file = new File([logContent], 'test.log', { type: 'text/plain' });
      
      const entries = await service.parseLogFile(file);
      
      expect(entries.length).toBe(5);
      expect(entries[0].level).toBe('INFO');
      expect(entries[1].level).toBe('DEBUG');
      expect(entries[2].level).toBe('WARN');
      expect(entries[3].level).toBe('ERROR');
      expect(entries[4].level).toBe('FATAL');
      expect(entries[4].source).toBe('Thread1');
    });
  });
});
