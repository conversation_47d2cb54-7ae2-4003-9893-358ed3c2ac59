.api-key-config-container {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.config-header {
  text-align: center;
  margin-bottom: 24px;
}

.config-header h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.config-header p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.status-section {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 1.2rem;
}

.status-text {
  font-weight: 600;
  color: #2c3e50;
}

.status-label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.status-value {
  font-weight: 600;
  color: #27ae60;
}

.input-section {
  margin-bottom: 24px;
}

.input-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.input-group {
  display: flex;
  gap: 8px;
}

.input-group input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  font-family: 'Courier New', monospace;
  transition: border-color 0.3s ease;
}

.input-group input:focus {
  outline: none;
  border-color: #3498db;
}

.input-group input:disabled {
  background-color: #f8f9fa;
  color: #7f8c8d;
}

.toggle-btn {
  padding: 12px 16px;
  background: #ecf0f1;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.toggle-btn:hover:not(:disabled) {
  background: #d5dbdb;
}

.toggle-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.save-btn, .clear-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #229954;
}

.save-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.clear-btn {
  background: #e74c3c;
  color: white;
}

.clear-btn:hover:not(:disabled) {
  background: #c0392b;
}

.clear-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-success {
  background: #d5f4e6;
  color: #27ae60;
  border: 1px solid #a9dfbf;
}

.message-error {
  background: #fadbd8;
  color: #e74c3c;
  border: 1px solid #f1948a;
}

.message-info {
  background: #d6eaf8;
  color: #3498db;
  border: 1px solid #aed6f1;
}

.message-icon {
  font-size: 1.1rem;
}

.instructions {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.instructions h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.instructions ol {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  color: #34495e;
}

.instructions a {
  color: #3498db;
  text-decoration: none;
}

.instructions a:hover {
  text-decoration: underline;
}

.warning {
  background: #fef9e7;
  border: 1px solid #f7dc6f;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.warning strong {
  color: #f39c12;
  display: block;
  margin-bottom: 8px;
}

.warning ul {
  margin: 0;
  padding-left: 20px;
  color: #7f8c8d;
}

.warning li {
  margin-bottom: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .api-key-config-container {
    padding: 16px;
    margin: 16px;
  }
  
  .status-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .toggle-btn {
    align-self: flex-end;
    width: fit-content;
  }
} 