.log-upload-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

.upload-header {
  text-align: center;
  margin-bottom: 2rem;
}

.upload-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.upload-header p {
  color: #7f8c8d;
  font-size: 0.95rem;
}

.upload-area {
  border: 2px dashed #bdc3c7;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: #f8f9fa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #3498db;
  background: #f0f8ff;
}

.upload-area.drag-over {
  border-color: #3498db;
  background: #e3f2fd;
  transform: scale(1.02);
}

.upload-area.has-file {
  border-color: #27ae60;
  background: #f0fff4;
}

.upload-content {
  width: 100%;
}

.upload-icon {
  color: #7f8c8d;
  margin-bottom: 1rem;
}

.upload-content h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  font-weight: 500;
}

.upload-content p {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
}

.file-input-label {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
  font-weight: 500;
}

.file-input-label:hover {
  background: #2980b9;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.file-icon {
  color: #27ae60;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-details h4 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 500;
}

.file-details p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.85rem;
}

.process-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
  flex-shrink: 0;
}

.process-btn:hover:not(:disabled) {
  background: #229954;
}

.process-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.progress-container {
  margin-top: 1.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fee;
  color: #c0392b;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
  border: 1px solid #fadbd8;
}

.error-message svg {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .log-upload-container {
    padding: 1rem;
  }
  
  .upload-area {
    padding: 2rem 1rem;
  }
  
  .file-info {
    flex-direction: column;
    text-align: center;
  }
  
  .file-details {
    text-align: center;
  }
} 