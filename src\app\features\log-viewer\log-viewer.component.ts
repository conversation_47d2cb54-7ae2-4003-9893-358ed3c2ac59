import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { LogEntry, LogEntryFilter } from '../../core/models/log-entry.model';
import { LogParserService } from '../../core/services/log-parser.service';
import { AppStateService } from '../../core/services/app-state.service';

@Component({
  selector: 'app-log-viewer',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './log-viewer.component.html',
  styleUrls: ['./log-viewer.component.css']
})
export class LogViewerComponent implements OnInit, OnDestroy {
  logEntries: LogEntry[] = [];
  filteredEntries: LogEntry[] = [];
  currentFilter: LogEntryFilter = {};
  
  // Filter options
  logLevels: LogEntry['level'][] = ['INFO', 'WARN', 'ERROR', 'DEBUG', 'FATAL'];
  selectedLevels: LogEntry['level'][] = [];
  searchTerm = '';
  startDate = '';
  endDate = '';
  
  // Pagination
  currentPage = 1;
  pageSize = 50;
  totalPages = 1;
  
  // Sorting
  sortBy: 'timestamp' | 'level' | 'message' = 'timestamp';
  sortOrder: 'asc' | 'desc' = 'desc';

  private stateSubscription: Subscription | null = null;

  constructor(
    private logParserService: LogParserService,
    private appStateService: AppStateService
  ) { }

  ngOnInit(): void {
    // Subscribe to state changes
    this.stateSubscription = this.appStateService.state$.subscribe(state => {
      this.logEntries = state.logEntries;
      this.applyFilters();
    });

    // Get initial state
    this.logEntries = this.appStateService.getLogEntries();
    this.applyFilters();
  }

  ngOnDestroy(): void {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  applyFilters(): void {
    let filtered = [...this.logEntries];

    // Apply level filter
    if (this.selectedLevels.length > 0) {
      filtered = filtered.filter(entry => this.selectedLevels.includes(entry.level));
    }

    // Apply search term
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(entry => 
        entry.message.toLowerCase().includes(term) ||
        entry.source?.toLowerCase().includes(term)
      );
    }

    // Apply date range
    if (this.startDate) {
      const start = new Date(this.startDate);
      filtered = filtered.filter(entry => entry.timestamp >= start);
    }

    if (this.endDate) {
      const end = new Date(this.endDate);
      end.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(entry => entry.timestamp <= end);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (this.sortBy) {
        case 'timestamp':
          aValue = a.timestamp.getTime();
          bValue = b.timestamp.getTime();
          break;
        case 'level':
          aValue = a.level;
          bValue = b.level;
          break;
        case 'message':
          aValue = a.message.toLowerCase();
          bValue = b.message.toLowerCase();
          break;
        default:
          return 0;
      }

      if (this.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    this.filteredEntries = filtered;
    this.updatePagination();
  }

  private updatePagination(): void {
    this.totalPages = Math.ceil(this.filteredEntries.length / this.pageSize);
    this.currentPage = Math.min(this.currentPage, this.totalPages);
    if (this.currentPage < 1) this.currentPage = 1;
  }

  getCurrentPageEntries(): LogEntry[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.filteredEntries.slice(startIndex, endIndex);
  }

  onLevelChange(level: LogEntry['level']): void {
    const index = this.selectedLevels.indexOf(level);
    if (index > -1) {
      this.selectedLevels.splice(index, 1);
    } else {
      this.selectedLevels.push(level);
    }
    this.currentPage = 1;
    this.applyFilters();
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onDateChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onSortChange(column: 'timestamp' | 'level' | 'message'): void {
    if (this.sortBy === column) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = column;
      this.sortOrder = 'desc';
    }
    this.applyFilters();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  getLevelClass(level: LogEntry['level']): string {
    return `log-level-${level.toLowerCase()}`;
  }

  getLevelIcon(level: LogEntry['level']): string {
    switch (level) {
      case 'ERROR':
      case 'FATAL':
        return '🔴';
      case 'WARN':
        return '🟡';
      case 'INFO':
        return '🔵';
      case 'DEBUG':
        return '⚪';
      default:
        return '⚪';
    }
  }

  formatTimestamp(timestamp: Date): string {
    return timestamp.toLocaleString();
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisible = 5;
    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(this.totalPages, start + maxVisible - 1);
    
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  clearFilters(): void {
    this.selectedLevels = [];
    this.searchTerm = '';
    this.startDate = '';
    this.endDate = '';
    this.currentPage = 1;
    this.applyFilters();
  }

  getFilterSummary(): string {
    const total = this.logEntries.length;
    const filtered = this.filteredEntries.length;
    const applied = total !== filtered;
    
    if (!applied) {
      return `Showing all ${total} entries`;
    }
    
    return `Showing ${filtered} of ${total} entries`;
  }
} 