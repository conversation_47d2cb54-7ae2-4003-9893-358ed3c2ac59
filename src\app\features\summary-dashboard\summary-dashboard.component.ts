import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { LogEntry } from '../../core/models/log-entry.model';
import { LogStatistics } from '../../core/models/log-summary.model';
import { AppStateService } from '../../core/services/app-state.service';

@Component({
  selector: 'app-summary-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './summary-dashboard.component.html',
  styleUrls: ['./summary-dashboard.component.css']
})
export class SummaryDashboardComponent implements OnInit, OnDestroy {
  logEntries: LogEntry[] = [];
  statistics: LogStatistics | null = null;

  private stateSubscription: Subscription | null = null;

  constructor(private appStateService: AppStateService) { }

  ngOnInit(): void {
    // Subscribe to state changes
    this.stateSubscription = this.appStateService.state$.subscribe(state => {
      this.logEntries = state.logEntries;
      this.statistics = state.logStatistics;
    });

    // Get initial state
    this.logEntries = this.appStateService.getLogEntries();
    this.statistics = this.appStateService.getLogStatistics();
  }

  ngOnDestroy(): void {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  getLevelCount(level: string): number {
    return this.logEntries.filter(entry => entry.level === level).length;
  }

  getLevelPercentage(level: string): number {
    if (this.logEntries.length === 0) return 0;
    return (this.getLevelCount(level) / this.logEntries.length) * 100;
  }

  getLevelClass(level: string): string {
    return `level-${level.toLowerCase()}`;
  }

  getLevelIcon(level: string): string {
    switch (level) {
      case 'ERROR':
      case 'FATAL':
        return '🔴';
      case 'WARN':
        return '🟡';
      case 'INFO':
        return '🔵';
      case 'DEBUG':
        return '⚪';
      default:
        return '⚪';
    }
  }

  formatTimeRange(): string {
    if (!this.statistics) return 'N/A';
    
    const start = this.statistics.summary.timeRange.start;
    const end = this.statistics.summary.timeRange.end;
    
    return `${start.toLocaleString()} - ${end.toLocaleString()}`;
  }

  formatFileSize(): string {
    if (!this.statistics) return 'N/A';
    
    const bytes = this.statistics.fileSize;
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatProcessingTime(): string {
    if (!this.statistics) return 'N/A';
    return `${(this.statistics.processingTime / 1000).toFixed(2)}s`;
  }

  getTopSources(): Array<{source: string, count: number}> {
    if (!this.statistics) return [];
    return this.statistics.summary.topSources.slice(0, 5);
  }

  getTopErrors(): Array<{message: string, count: number, level: string}> {
    if (!this.statistics) return [];
    return this.statistics.summary.topErrors.slice(0, 5);
  }

  getHourlyDistribution(): Array<{hour: number, count: number}> {
    if (!this.statistics) return [];
    return this.statistics.summary.hourlyDistribution;
  }

  getHourLabel(hour: number): string {
    return `${hour.toString().padStart(2, '0')}:00`;
  }

  getMaxHourlyCount(): number {
    if (!this.statistics) return 0;
    const counts = this.statistics.summary.hourlyDistribution.map(h => h.count);
    return Math.max(...counts, 1);
  }

  getHourlyPercentage(count: number): number {
    const max = this.getMaxHourlyCount();
    return (count / max) * 100;
  }
} 