export interface LogSummary {
  totalEntries: number;
  errorCount: number;
  warningCount: number;
  infoCount: number;
  debugCount: number;
  fatalCount: number;
  timeRange: {
    start: Date;
    end: Date;
  };
  topSources: Array<{
    source: string;
    count: number;
  }>;
  topErrors: Array<{
    message: string;
    count: number;
    level: string;
  }>;
  hourlyDistribution: Array<{
    hour: number;
    count: number;
  }>;
}

export interface LogStatistics {
  summary: LogSummary;
  processingTime: number;
  fileSize: number;
  fileName: string;
} 