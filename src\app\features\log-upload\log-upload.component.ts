import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { LogEntry } from '../../core/models/log-entry.model';
import { LogStatistics } from '../../core/models/log-summary.model';
import { LogParserService } from '../../core/services/log-parser.service';
import { SummaryGeneratorService } from '../../core/services/summary-generator.service';
import { AppStateService } from '../../core/services/app-state.service';

@Component({
  selector: 'app-log-upload',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './log-upload.component.html',
  styleUrls: ['./log-upload.component.css']
})
export class LogUploadComponent {
  selectedFile: File | null = null;
  isProcessing = false;
  processingProgress = 0;
  errorMessage = '';

  constructor(
    private logParserService: LogParserService,
    private summaryGeneratorService: SummaryGeneratorService,
    private appStateService: AppStateService,
    private router: Router
  ) { }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file && this.isValidLogFile(file)) {
      this.selectedFile = file;
      this.errorMessage = '';
    } else {
      this.errorMessage = 'Please select a valid log file (.log, .txt)';
      this.selectedFile = null;
    }
  }

  private isValidLogFile(file: File): boolean {
    const validExtensions = ['.log', '.txt'];
    const fileName = file.name.toLowerCase();
    return validExtensions.some(ext => fileName.endsWith(ext));
  }

  async processLogFile(): Promise<void> {
    if (!this.selectedFile) {
      this.errorMessage = 'Please select a file first';
      return;
    }

    this.isProcessing = true;
    this.processingProgress = 0;
    this.errorMessage = '';

    try {
      const startTime = Date.now();

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        this.processingProgress += 10;
        if (this.processingProgress >= 90) {
          clearInterval(progressInterval);
        }
      }, 100);

      const entries = await this.logParserService.parseLogFile(this.selectedFile);
      const processingTime = Date.now() - startTime;

      const statistics = this.summaryGeneratorService.generateLogStatistics(
        entries,
        this.selectedFile.name,
        processingTime
      );

      this.processingProgress = 100;

      // Update the shared state with the parsed data
      this.appStateService.setLogEntries(entries);
      this.appStateService.setLogStatistics(statistics);

      // Navigate to the viewer to show the results
      setTimeout(() => {
        this.router.navigate(['/viewer']);
      }, 500);

    } catch (error) {
      this.errorMessage = 'Error processing log file: ' + (error as Error).message;
      this.isProcessing = false;
      this.processingProgress = 0;
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (this.isValidLogFile(file)) {
        this.selectedFile = file;
        this.errorMessage = '';
      } else {
        this.errorMessage = 'Please drop a valid log file (.log, .txt)';
      }
    }
  }

  getFileSize(): string {
    if (!this.selectedFile) return '';
    
    const bytes = this.selectedFile.size;
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
} 