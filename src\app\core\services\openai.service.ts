import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface OpenAIRequest {
  logEntries: string[];
  analysisType: string;
  context?: string;
}

export interface OpenAIResponse {
  summary: string;
  insights: any[];
  recommendations: any[];
  confidence: number;
}

@Injectable({ providedIn: 'root' })
export class OpenAIService {
  private readonly apiUrl = 'https://api.openai.com/v1/chat/completions';

  constructor(private http: HttpClient) {}

  analyzeLogs(request: OpenAIRequest): Observable<OpenAIResponse> {
    const apiKey = environment.openai.apiKey;
    if (!apiKey) {
      return throwError(() => new Error('OpenAI API key is not configured.'));
    }

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    });

    const prompt = this.buildAnalysisPrompt(request);

    const body = {
      model: environment.openai.model || 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: this.getSystemPrompt() },
        { role: 'user', content: prompt }
      ],
      max_tokens: environment.openai.maxTokens || 512,
      temperature: environment.openai.temperature || 0.2
    };

    return this.http.post<any>(this.apiUrl, body, { headers }).pipe(
      map(res => this.parseAIResponse(res)),
      catchError(err => throwError(() => new Error(this.handleAPIError(err))))
    );
  }

  private getSystemPrompt(): string {
    return 'You are an expert log analysis assistant. Analyze the provided logs and provide a summary, insights, and recommendations.';
  }

  private buildAnalysisPrompt(request: OpenAIRequest): string {
    let prompt = `Analyze the following logs for ${request.analysisType}.\n`;
    if (request.context) {
      prompt += `Context: ${request.context}\n`;
    }
    prompt += 'Logs:\n';
    prompt += request.logEntries.slice(0, 50).join('\n'); // Limit to 50 lines for token safety
    return prompt;
  }

  private parseAIResponse(res: any): OpenAIResponse {
    // This assumes OpenAI returns a single message in choices[0].message.content
    const content = res?.choices?.[0]?.message?.content || '';
    // You can improve this parsing based on your prompt engineering
    return {
      summary: content,
      insights: [],
      recommendations: [],
      confidence: 0.9
    };
  }

  private handleAPIError(error: any): string {
    if (error?.error?.error?.message) return error.error.error.message;
    if (error?.message) return error.message;
    return 'Unknown error occurred while calling OpenAI API.';
  }

  // Utility methods for UI
  isConfigured(): boolean {
    return !!environment.openai.apiKey;
  }
} 