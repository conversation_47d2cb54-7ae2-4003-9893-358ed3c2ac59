<div class="ai-analysis-container">
  <div class="analysis-header">
    <h2>AI-Powered Log Analysis</h2>
    <p>Use artificial intelligence to analyze your log files for patterns, insights, and recommendations.</p>
  </div>

  <!-- API Key Configuration -->
  <div class="api-config-section">
    <app-api-key-config></app-api-key-config>
  </div>

  <!-- Analysis Controls -->
  <div class="analysis-controls" *ngIf="logEntries.length > 0">
    <div class="control-group">
      <label for="analysis-type">Analysis Type:</label>
      <select 
        id="analysis-type"
        [(ngModel)]="selectedAnalysisType"
        [disabled]="isAnalyzing">
        <option 
          *ngFor="let type of analysisTypes" 
          [value]="type.value">
          {{ type.icon }} {{ type.label }}
        </option>
      </select>
    </div>

    <div class="control-group">
      <label for="context-input">Additional Context (Optional):</label>
      <textarea 
        id="context-input"
        [(ngModel)]="context"
        placeholder="Provide additional context about your system or specific concerns..."
        rows="3"
        [disabled]="isAnalyzing">
      </textarea>
    </div>

    <button 
      class="analyze-btn"
      (click)="runAnalysis()"
      [disabled]="!canRunAnalysis()">
      <span *ngIf="!isAnalyzing">🤖 Run AI Analysis</span>
      <span *ngIf="isAnalyzing">⏳ Analyzing...</span>
    </button>
  </div>

  <!-- Error Message -->
  <div class="error-message" *ngIf="errorMessage">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="10"/>
      <line x1="15" y1="9" x2="9" y2="15"/>
      <line x1="9" y1="9" x2="15" y2="15"/>
    </svg>
    {{ errorMessage }}
  </div>

  <!-- Analysis Results -->
  <div class="analysis-results" *ngIf="analysisResult">
    <!-- Summary -->
    <div class="result-summary">
      <div class="summary-header">
        <h3>{{ getAnalysisTypeIcon(analysisResult.analysisType) }} {{ getAnalysisTypeLabel(analysisResult.analysisType) }}</h3>
        <div class="summary-meta">
          <span class="confidence">Confidence: {{ formatConfidence(analysisResult.confidence) }}</span>
          <span class="processing-time">Processing Time: {{ formatProcessingTime(analysisResult.processingTime) }}</span>
        </div>
      </div>
      <p class="summary-text">{{ analysisResult.summary }}</p>
    </div>

    <!-- Insights -->
    <div class="insights-section" *ngIf="analysisResult.insights.length > 0">
      <h3>🔍 Insights ({{ analysisResult.insights.length }})</h3>
      <div class="insights-grid">
        <div class="insight-card" *ngFor="let insight of analysisResult.insights">
          <div class="insight-header">
            <span class="insight-icon">{{ getInsightIcon(insight.type) }}</span>
            <span class="insight-title">{{ insight.title }}</span>
            <span class="severity-badge" [class]="getSeverityClass(insight.severity)">
              {{ insight.severity }}
            </span>
          </div>
          <p class="insight-description">{{ insight.description }}</p>
          <div class="insight-meta">
            <span class="affected-entries">{{ insight.affectedEntries }} affected entries</span>
          </div>
          <div class="insight-examples" *ngIf="insight.examples.length > 0">
            <h4>Examples:</h4>
            <ul>
              <li *ngFor="let example of insight.examples.slice(0, 2)">
                {{ example }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Recommendations -->
    <div class="recommendations-section" *ngIf="analysisResult.recommendations.length > 0">
      <h3>💡 Recommendations ({{ analysisResult.recommendations.length }})</h3>
      <div class="recommendations-grid">
        <div class="recommendation-card" *ngFor="let rec of analysisResult.recommendations">
          <div class="recommendation-header">
            <span class="recommendation-title">{{ rec.title }}</span>
            <span class="priority-badge" [class]="getPriorityClass(rec.priority)">
              {{ rec.priority }}
            </span>
          </div>
          <p class="recommendation-description">{{ rec.description }}</p>
          <div class="recommendation-details">
            <div class="detail-item">
              <strong>Action:</strong> {{ rec.action }}
            </div>
            <div class="detail-item">
              <strong>Impact:</strong> {{ rec.impact }}
            </div>
            <div class="detail-item">
              <strong>Effort:</strong> {{ rec.effort }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- No Log Data Message -->
  <div class="no-data-message" *ngIf="logEntries.length === 0">
    <div class="no-data-content">
      <h3>📋 No Log Data Available</h3>
      <p>Please upload log files first to enable AI analysis.</p>
      <a routerLink="/upload" class="upload-link">Go to Upload</a>
    </div>
  </div>
</div> 