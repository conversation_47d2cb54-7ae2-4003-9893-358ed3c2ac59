import { Injectable } from '@angular/core';
import { LogEntry, LogEntryFilter } from '../models/log-entry.model';
import { LogSummary } from '../models/log-summary.model';

@Injectable({
  providedIn: 'root'
})
export class LogParserService {

  constructor() { }

  parseLogFile(file: File): Promise<LogEntry[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const lines = content.split('\n');
          const entries: LogEntry[] = [];
          
          lines.forEach((line, index) => {
            if (line.trim()) {
              const entry = this.parseLogLine(line, index.toString());
              if (entry) {
                entries.push(entry);
              }
            }
          });
          
          resolve(entries);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  private parseLogLine(line: string, id: string): LogEntry | null {
    // More robust pattern: allow multiple spaces/tabs and both ' ' and 'T' as date/time separator
    const pattern = /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(\w+)[ \t]+(.+)$/;
    const match = line.match(pattern);

    if (match) {
      const timestamp = this.parseTimestamp(match[1]);
      const level = this.parseLogLevel(match[2] || 'INFO');
      const message = match[3] || match[2] || line;

      return {
        id,
        timestamp,
        level,
        message: message.trim(),
        rawLine: line
      };
    }

    // Fallback: treat as INFO level
    return {
      id,
      timestamp: new Date(),
      level: 'INFO',
      message: line.trim(),
      rawLine: line
    };
  }

  private parseTimestamp(timestampStr: string): Date {
    try {
      return new Date(timestampStr);
    } catch {
      return new Date();
    }
  }

  private parseLogLevel(levelStr: string): LogEntry['level'] {
    const level = levelStr.toUpperCase();
    if (['ERROR', 'WARN', 'INFO', 'DEBUG', 'FATAL'].includes(level)) {
      return level as LogEntry['level'];
    }
    return 'INFO';
  }

  filterLogEntries(entries: LogEntry[], filter: LogEntryFilter): LogEntry[] {
    return entries.filter(entry => {
      if (filter.level && entry.level !== filter.level) return false;
      if (filter.source && entry.source !== filter.source) return false;
      if (filter.startDate && entry.timestamp < filter.startDate) return false;
      if (filter.endDate && entry.timestamp > filter.endDate) return false;
      if (filter.searchTerm && !entry.message.toLowerCase().includes(filter.searchTerm.toLowerCase())) return false;
      return true;
    });
  }

  generateSummary(entries: LogEntry[]): LogSummary {
    const levelCounts = { ERROR: 0, WARN: 0, INFO: 0, DEBUG: 0, FATAL: 0 };
    const sources: Record<string, number> = {};
    const errors: Record<string, number> = {};
    const timestamps = entries.map(e => e.timestamp);

    entries.forEach(entry => {
      levelCounts[entry.level]++;
      
      if (entry.source) {
        sources[entry.source] = (sources[entry.source] || 0) + 1;
      }
      
      if (entry.level === 'ERROR' || entry.level === 'FATAL') {
        const key = entry.message.substring(0, 100);
        errors[key] = (errors[key] || 0) + 1;
      }
    });

    const hourlyDistribution = this.generateHourlyDistribution(entries);

    return {
      totalEntries: entries.length,
      errorCount: levelCounts.ERROR,
      warningCount: levelCounts.WARN,
      infoCount: levelCounts.INFO,
      debugCount: levelCounts.DEBUG,
      fatalCount: levelCounts.FATAL,
      timeRange: {
        start: new Date(Math.min(...timestamps.map(t => t.getTime()))),
        end: new Date(Math.max(...timestamps.map(t => t.getTime())))
      },
      topSources: Object.entries(sources)
        .map(([source, count]) => ({ source, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      topErrors: Object.entries(errors)
        .map(([message, count]) => ({ 
          message, 
          count, 
          level: 'ERROR' 
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      hourlyDistribution
    };
  }

  private generateHourlyDistribution(entries: LogEntry[]): Array<{hour: number, count: number}> {
    const hourly: Record<number, number> = {};
    
    for (let i = 0; i < 24; i++) {
      hourly[i] = 0;
    }
    
    entries.forEach(entry => {
      const hour = entry.timestamp.getHours();
      hourly[hour]++;
    });
    
    return Object.entries(hourly).map(([hour, count]) => ({
      hour: parseInt(hour),
      count
    }));
  }
} 