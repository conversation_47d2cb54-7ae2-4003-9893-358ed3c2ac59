import { Injectable } from '@angular/core';
import { LogEntry, LogEntryFilter } from '../models/log-entry.model';
import { LogSummary } from '../models/log-summary.model';

@Injectable({
  providedIn: 'root'
})
export class LogParserService {

  constructor() { }

  parseLogFile(file: File): Promise<LogEntry[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const lines = content.split('\n');
          const entries: LogEntry[] = [];
          let parsedCount = 0;
          let fallbackCount = 0;

          lines.forEach((line, index) => {
            if (line.trim()) {
              const entry = this.parseLogLine(line, index.toString());
              if (entry) {
                entries.push(entry);
                // Track parsing success
                if (this.isStructuredLogLine(line)) {
                  parsedCount++;
                } else {
                  fallbackCount++;
                }
              }
            }
          });

          // Log parsing statistics for debugging
          console.log(`Log parsing completed: ${entries.length} total entries, ${parsedCount} structured, ${fallbackCount} fallback`);
          this.logLevelDistribution(entries);

          resolve(entries);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  private isStructuredLogLine(line: string): boolean {
    // Check if line matches any of our structured patterns
    const patterns = [
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(\w+)[ \t]+(.+)$/,
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+\[(\w+)\][ \t]+(.+)$/,
      /^(\w+)[ \t]+(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(.+)$/,
      /^\[(\w+)\][ \t]+(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(.+)$/,
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+\[(\w+)\][ \t]+\[([^\]]+)\][ \t]+(.+)$/,
      /^(\w+):[ \t]+(.+)$/,
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(\w+):[ \t]+(.+)$/
    ];

    return patterns.some(pattern => pattern.test(line));
  }

  private logLevelDistribution(entries: LogEntry[]): void {
    const distribution = entries.reduce((acc, entry) => {
      acc[entry.level] = (acc[entry.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('Log level distribution:', distribution);
  }

  private parseLogLine(line: string, id: string): LogEntry | null {
    // Try multiple log format patterns to support various log formats
    const patterns = [
      // Standard format: 2024-01-15 10:30:15 INFO Message
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(\w+)[ \t]+(.+)$/,

      // Format with brackets: 2024-01-15 10:30:15 [INFO] Message
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+\[(\w+)\][ \t]+(.+)$/,

      // Format with level first: INFO 2024-01-15 10:30:15 Message
      /^(\w+)[ \t]+(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(.+)$/,

      // Format with level in brackets first: [INFO] 2024-01-15 10:30:15 Message
      /^\[(\w+)\][ \t]+(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(.+)$/,

      // Format with thread/source: 2024-01-15 10:30:15 [INFO] [ThreadName] Message
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+\[(\w+)\][ \t]+\[([^\]]+)\][ \t]+(.+)$/,

      // Simple format: INFO: Message (no timestamp)
      /^(\w+):[ \t]+(.+)$/,

      // Format with colon: 2024-01-15 10:30:15 INFO: Message
      /^(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}(?:\.\d+)?)[ \t]+(\w+):[ \t]+(.+)$/
    ];

    for (const pattern of patterns) {
      const match = line.match(pattern);
      if (match) {
        return this.createLogEntryFromMatch(match, pattern, id, line);
      }
    }

    // Fallback: treat as INFO level
    return {
      id,
      timestamp: new Date(),
      level: 'INFO',
      message: line.trim(),
      rawLine: line
    };
  }

  private createLogEntryFromMatch(match: RegExpMatchArray, pattern: RegExp, id: string, line: string): LogEntry {
    const patternStr = pattern.toString();
    let timestamp: Date;
    let level: LogEntry['level'];
    let message: string;
    let source: string | undefined;

    if (patternStr.includes('\\[([^\\]]+)\\].*\\[([^\\]]+)\\]')) {
      // Pattern with thread/source: timestamp [level] [source] message
      timestamp = this.parseTimestamp(match[1]);
      level = this.parseLogLevel(match[2]);
      source = match[3];
      message = match[4];
    } else if (patternStr.includes('^(\\w+).*\\d{4}')) {
      // Pattern with level first: level timestamp message
      level = this.parseLogLevel(match[1]);
      timestamp = this.parseTimestamp(match[2]);
      message = match[3];
    } else if (patternStr.includes('^\\[(\\w+)\\].*\\d{4}')) {
      // Pattern with [level] first: [level] timestamp message
      level = this.parseLogLevel(match[1]);
      timestamp = this.parseTimestamp(match[2]);
      message = match[3];
    } else if (patternStr.includes('^(\\w+):')) {
      // Simple pattern: level: message (no timestamp)
      level = this.parseLogLevel(match[1]);
      timestamp = new Date();
      message = match[2];
    } else {
      // Standard patterns: timestamp level message
      timestamp = this.parseTimestamp(match[1]);
      level = this.parseLogLevel(match[2]);
      message = match[3];
    }

    return {
      id,
      timestamp,
      level,
      message: message.trim(),
      source: source?.trim(),
      rawLine: line
    };
  }

  private parseTimestamp(timestampStr: string): Date {
    try {
      return new Date(timestampStr);
    } catch {
      return new Date();
    }
  }

  private parseLogLevel(levelStr: string): LogEntry['level'] {
    const level = levelStr.toUpperCase().trim();

    // Handle various log level formats and aliases
    switch (level) {
      case 'ERROR':
      case 'ERR':
      case 'SEVERE':
        return 'ERROR';

      case 'WARN':
      case 'WARNING':
      case 'CAUTION':
        return 'WARN';

      case 'INFO':
      case 'INFORMATION':
      case 'NOTICE':
        return 'INFO';

      case 'DEBUG':
      case 'DBG':
      case 'TRACE':
      case 'VERBOSE':
        return 'DEBUG';

      case 'FATAL':
      case 'CRITICAL':
      case 'CRIT':
      case 'EMERGENCY':
      case 'EMERG':
        return 'FATAL';

      default:
        return 'INFO';
    }
  }

  filterLogEntries(entries: LogEntry[], filter: LogEntryFilter): LogEntry[] {
    return entries.filter(entry => {
      if (filter.level && entry.level !== filter.level) return false;
      if (filter.source && entry.source !== filter.source) return false;
      if (filter.startDate && entry.timestamp < filter.startDate) return false;
      if (filter.endDate && entry.timestamp > filter.endDate) return false;
      if (filter.searchTerm && !entry.message.toLowerCase().includes(filter.searchTerm.toLowerCase())) return false;
      return true;
    });
  }

  generateSummary(entries: LogEntry[]): LogSummary {
    const levelCounts = { ERROR: 0, WARN: 0, INFO: 0, DEBUG: 0, FATAL: 0 };
    const sources: Record<string, number> = {};
    const errors: Record<string, number> = {};
    const timestamps = entries.map(e => e.timestamp);

    entries.forEach(entry => {
      levelCounts[entry.level]++;
      
      if (entry.source) {
        sources[entry.source] = (sources[entry.source] || 0) + 1;
      }
      
      if (entry.level === 'ERROR' || entry.level === 'FATAL') {
        const key = entry.message.substring(0, 100);
        errors[key] = (errors[key] || 0) + 1;
      }
    });

    const hourlyDistribution = this.generateHourlyDistribution(entries);

    return {
      totalEntries: entries.length,
      errorCount: levelCounts.ERROR,
      warningCount: levelCounts.WARN,
      infoCount: levelCounts.INFO,
      debugCount: levelCounts.DEBUG,
      fatalCount: levelCounts.FATAL,
      timeRange: {
        start: new Date(Math.min(...timestamps.map(t => t.getTime()))),
        end: new Date(Math.max(...timestamps.map(t => t.getTime())))
      },
      topSources: Object.entries(sources)
        .map(([source, count]) => ({ source, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      topErrors: Object.entries(errors)
        .map(([message, count]) => ({ 
          message, 
          count, 
          level: 'ERROR' 
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      hourlyDistribution
    };
  }

  private generateHourlyDistribution(entries: LogEntry[]): Array<{hour: number, count: number}> {
    const hourly: Record<number, number> = {};
    
    for (let i = 0; i < 24; i++) {
      hourly[i] = 0;
    }
    
    entries.forEach(entry => {
      const hour = entry.timestamp.getHours();
      hourly[hour]++;
    });
    
    return Object.entries(hourly).map(([hour, count]) => ({
      hour: parseInt(hour),
      count
    }));
  }
} 