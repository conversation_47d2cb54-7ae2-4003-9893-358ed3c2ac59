import { Injectable } from '@angular/core';
import { LogEntry } from '../models/log-entry.model';
import { LogSummary, LogStatistics } from '../models/log-summary.model';

@Injectable({
  providedIn: 'root'
})
export class SummaryGeneratorService {

  constructor() { }

  generateLogStatistics(entries: LogEntry[], fileName: string, processingTime: number): LogStatistics {
    const summary = this.generateSummary(entries);
    
    return {
      summary,
      processingTime,
      fileSize: this.calculateFileSize(entries),
      fileName
    };
  }

  private generateSummary(entries: LogEntry[]): LogSummary {
    const levelCounts = { ERROR: 0, WARN: 0, INFO: 0, DEBUG: 0, FATAL: 0 };
    const sources: Record<string, number> = {};
    const errors: Record<string, number> = {};
    const timestamps = entries.map(e => e.timestamp);

    entries.forEach(entry => {
      levelCounts[entry.level]++;
      
      if (entry.source) {
        sources[entry.source] = (sources[entry.source] || 0) + 1;
      }
      
      if (entry.level === 'ERROR' || entry.level === 'FATAL') {
        const key = entry.message.substring(0, 100);
        errors[key] = (errors[key] || 0) + 1;
      }
    });

    return {
      totalEntries: entries.length,
      errorCount: levelCounts.ERROR,
      warningCount: levelCounts.WARN,
      infoCount: levelCounts.INFO,
      debugCount: levelCounts.DEBUG,
      fatalCount: levelCounts.FATAL,
      timeRange: {
        start: new Date(Math.min(...timestamps.map(t => t.getTime()))),
        end: new Date(Math.max(...timestamps.map(t => t.getTime())))
      },
      topSources: Object.entries(sources)
        .map(([source, count]) => ({ source, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      topErrors: Object.entries(errors)
        .map(([message, count]) => ({ 
          message, 
          count, 
          level: 'ERROR' 
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      hourlyDistribution: this.generateHourlyDistribution(entries)
    };
  }

  private generateHourlyDistribution(entries: LogEntry[]): Array<{hour: number, count: number}> {
    const hourly: Record<number, number> = {};
    
    for (let i = 0; i < 24; i++) {
      hourly[i] = 0;
    }
    
    entries.forEach(entry => {
      const hour = entry.timestamp.getHours();
      hourly[hour]++;
    });
    
    return Object.entries(hourly).map(([hour, count]) => ({
      hour: parseInt(hour),
      count
    }));
  }

  private calculateFileSize(entries: LogEntry[]): number {
    return entries.reduce((total, entry) => {
      return total + (entry.rawLine?.length || entry.message.length);
    }, 0);
  }

  getErrorRate(entries: LogEntry[]): number {
    const errorCount = entries.filter(e => e.level === 'ERROR' || e.level === 'FATAL').length;
    return entries.length > 0 ? (errorCount / entries.length) * 100 : 0;
  }

  getAverageEntriesPerHour(entries: LogEntry[]): number {
    if (entries.length === 0) return 0;
    
    const timeRange = this.getTimeRange(entries);
    const hoursDiff = (timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60);
    
    return hoursDiff > 0 ? entries.length / hoursDiff : entries.length;
  }

  private getTimeRange(entries: LogEntry[]) {
    const timestamps = entries.map(e => e.timestamp);
    return {
      start: new Date(Math.min(...timestamps.map(t => t.getTime()))),
      end: new Date(Math.max(...timestamps.map(t => t.getTime())))
    };
  }
} 