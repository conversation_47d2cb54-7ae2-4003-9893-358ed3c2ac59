<div class="app-container">
  <!-- Header -->
  <header class="app-header">
    <div class="header-content">
      <div class="logo">
        <h1>{{ title }}</h1>
        <p>Intelligent Log Analysis with AI</p>
      </div>
      
      <nav class="main-nav">
        <a routerLink="/upload" routerLinkActive="active" class="nav-link">
          📤 Upload
        </a>
        <a routerLink="/viewer" routerLinkActive="active" class="nav-link" [class.disabled]="!hasLogData()">
          📋 Viewer
        </a>
        <a routerLink="/dashboard" routerLinkActive="active" class="nav-link" [class.disabled]="!hasLogData()">
          📊 Dashboard
        </a>
        <a routerLink="/analysis" routerLinkActive="active" class="nav-link" [class.disabled]="!hasLogData()">
          🤖 AI Analysis
        </a>
      </nav>

      <!-- Status Indicator -->
      <div class="status-indicator" *ngIf="hasLogData()">
        <span class="status-text">
          📄 {{ getLogCount() }} entries | 
          🔴 {{ getErrorCount() }} errors
        </span>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="app-main">
    <router-outlet></router-outlet>
  </main>

  <!-- Footer -->
  <footer class="app-footer">
    <p>&copy; 2024 AI Log Analyzer. Built with Angular and AI.</p>
  </footer>
</div>