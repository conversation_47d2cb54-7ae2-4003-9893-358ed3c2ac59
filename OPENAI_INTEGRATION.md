# OpenAI Integration Guide

This document explains how to integrate real OpenAI API functionality into the AI Log Analyzer application.

## Overview

The application now supports both simulated AI responses (for development/testing) and real OpenAI API integration (for production use). The system automatically falls back to simulated responses if the OpenAI API is not configured or encounters errors.

## Features

### ✅ Implemented Features

1. **Real OpenAI API Integration**
   - Direct API calls to OpenAI's GPT models
   - Structured JSON responses for consistent data format
   - Automatic fallback to simulated responses

2. **API Key Management**
   - Secure API key configuration interface
   - Environment-based configuration
   - Cost monitoring and usage tracking

3. **Error Handling & Rate Limiting**
   - Comprehensive error handling for API failures
   - Rate limiting to prevent API quota exhaustion
   - Retry logic with exponential backoff
   - User-friendly error messages

4. **Cost Management**
   - Real-time cost estimation
   - Request count tracking
   - Usage monitoring dashboard

## Setup Instructions

### 1. Get OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in or create an account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (starts with "sk-")

### 2. Configure API Key

#### Option A: Environment Configuration (Recommended for Production)

Edit the environment files:

```typescript
// src/environments/environment.ts (Development)
export const environment = {
  // ... other config
  openai: {
    apiKey: 'your-api-key-here', // Add your key here
    model: 'gpt-4', // or 'gpt-3.5-turbo'
    maxTokens: 2000,
    temperature: 0.3,
    timeout: 30000,
    retryAttempts: 3
  }
};
```

#### Option B: UI Configuration (Development/Testing)

1. Start the application: `npm start`
2. Navigate to the AI Analysis page
3. Use the API Key Configuration section
4. Enter your OpenAI API key
5. Click "Save API Key"

### 3. Test the Integration

1. Upload a log file
2. Go to AI Analysis page
3. Select an analysis type
4. Click "Run AI Analysis"
5. Verify real AI responses are received

## Configuration Options

### Environment Variables

```typescript
openai: {
  apiKey: string,           // Your OpenAI API key
  model: string,            // 'gpt-4' or 'gpt-3.5-turbo'
  maxTokens: number,        // Maximum tokens per request (default: 2000)
  temperature: number,      // Response creativity (0.0-1.0, default: 0.3)
  timeout: number,          // Request timeout in ms (default: 30000)
  retryAttempts: number     // Number of retry attempts (default: 3)
}
```

### Model Selection

- **GPT-4**: More accurate, higher cost (~$0.03 per 1K tokens)
- **GPT-3.5-turbo**: Faster, lower cost (~$0.002 per 1K tokens)

## Security Best Practices

### 1. API Key Security

```typescript
// ❌ Never commit API keys to version control
export const environment = {
  openai: {
    apiKey: 'sk-1234567890abcdef' // DON'T DO THIS
  }
};

// ✅ Use environment variables or secure storage
export const environment = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY || ''
  }
};
```

### 2. Production Deployment

For production, use environment variables:

```bash
# .env file (not committed to git)
OPENAI_API_KEY=sk-your-actual-key-here
```

```typescript
// environment.prod.ts
export const environment = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    model: 'gpt-4',
    // ... other config
  }
};
```

### 3. Rate Limiting

The application includes built-in rate limiting:

- 1 second delay between requests
- Automatic retry with exponential backoff
- Request count tracking

## Error Handling

### Common Error Scenarios

1. **Invalid API Key (401)**
   - Check API key format (should start with "sk-")
   - Verify key is active in OpenAI dashboard

2. **Rate Limit Exceeded (429)**
   - Wait before making another request
   - Consider upgrading OpenAI plan

3. **Payment Required (402)**
   - Add billing information to OpenAI account
   - Check usage limits

4. **Service Unavailable (5xx)**
   - Temporary OpenAI service issues
   - Application falls back to simulated responses

### Error Recovery

The application automatically handles errors:

```typescript
try {
  // Try real OpenAI API
  return await this.useRealAI(logStrings, analysisType, context, startTime);
} catch (error) {
  console.warn('AI service error, falling back to simulated response:', error);
  // Fallback to simulated AI
  return await this.useSimulatedAI(entries, analysisType, startTime);
}
```

## Cost Management

### Cost Estimation

The application tracks estimated costs:

```typescript
getEstimatedCost(): number {
  const costPer1kTokens = environment.openai.model === 'gpt-4' ? 0.03 : 0.002;
  const averageTokensPerRequest = 1500;
  return (this.requestCount * averageTokensPerRequest * costPer1kTokens) / 1000;
}
```

### Cost Optimization Tips

1. **Use GPT-3.5-turbo for testing**
   - 15x cheaper than GPT-4
   - Sufficient for most log analysis tasks

2. **Limit context length**
   - Shorter prompts = lower costs
   - Focus on most relevant log entries

3. **Monitor usage**
   - Check cost estimates in the UI
   - Set up OpenAI usage alerts

## API Response Format

The OpenAI service expects structured JSON responses:

```json
{
  "summary": "Analysis summary text",
  "insights": [
    {
      "type": "error_pattern|performance_issue|security_concern|trend|anomaly",
      "title": "Insight title",
      "description": "Detailed description",
      "severity": "low|medium|high|critical",
      "affectedEntries": 5,
      "examples": ["example1", "example2"]
    }
  ],
  "recommendations": [
    {
      "title": "Recommendation title",
      "description": "Description",
      "priority": "low|medium|high|critical",
      "action": "Specific action to take",
      "impact": "Expected impact",
      "effort": "low|medium|high"
    }
  ],
  "confidence": 0.85
}
```

## Troubleshooting

### Common Issues

1. **"OpenAI client not initialized"**
   - Check API key configuration
   - Verify environment file settings

2. **"Rate limit exceeded"**
   - Wait 1 minute before retrying
   - Check OpenAI account usage

3. **"Invalid response format"**
   - OpenAI returned malformed JSON
   - Application falls back to simulated response

4. **"Payment required"**
   - Add billing to OpenAI account
   - Check account balance

### Debug Mode

Enable debug logging:

```typescript
// In openai.service.ts
console.log('OpenAI request:', request);
console.log('OpenAI response:', response);
```

## Performance Considerations

### Request Optimization

1. **Token Limits**
   - Keep prompts under 2000 tokens
   - Truncate long log entries if needed

2. **Response Time**
   - GPT-4: ~2-5 seconds per request
   - GPT-3.5-turbo: ~1-3 seconds per request

3. **Concurrent Requests**
   - Application enforces rate limiting
   - Consider queue system for high-volume usage

## Future Enhancements

### Planned Features

1. **Streaming Responses**
   - Real-time AI analysis updates
   - Progress indicators

2. **Advanced Prompting**
   - Custom analysis templates
   - Industry-specific prompts

3. **Cost Controls**
   - Usage limits and alerts
   - Budget management

4. **Caching**
   - Cache similar analysis results
   - Reduce API calls and costs

## Support

For issues with OpenAI integration:

1. Check this documentation
2. Review browser console for errors
3. Verify API key and billing status
4. Test with OpenAI's playground first

## License

This integration follows OpenAI's usage policies and terms of service. Ensure compliance with OpenAI's API usage guidelines. 