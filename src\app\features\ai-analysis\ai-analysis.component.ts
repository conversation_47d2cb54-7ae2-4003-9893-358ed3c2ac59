import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { LogEntry } from '../../core/models/log-entry.model';
import { AIAnalysisResponse, AIInsight, AIRecommendation } from '../../core/models/ai-response.model';
import { AIServiceService } from '../../core/services/ai-service.service';
import { ApiKeyConfigComponent } from '../../shared/components/api-key-config/api-key-config.component';
import { AppStateService } from '../../core/services/app-state.service';

@Component({
  selector: 'app-ai-analysis',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, ApiKeyConfigComponent],
  templateUrl: './ai-analysis.component.html',
  styleUrls: ['./ai-analysis.component.css']
})
export class AIAnalysisComponent implements OnInit, OnDestroy {
  logEntries: LogEntry[] = [];

  analysisTypes = [
    { value: 'error_patterns', label: 'Error Patterns', icon: '🔍' },
    { value: 'performance_issues', label: 'Performance Issues', icon: '⚡' },
    { value: 'security_concerns', label: 'Security Concerns', icon: '🔒' },
    { value: 'general_insights', label: 'General Insights', icon: '💡' }
  ];

  selectedAnalysisType = 'error_patterns';
  context = '';
  isAnalyzing = false;
  analysisResult: AIAnalysisResponse | null = null;
  errorMessage = '';

  private stateSubscription: Subscription | null = null;

  constructor(
    private aiService: AIServiceService,
    private appStateService: AppStateService
  ) { }

  ngOnInit(): void {
    // Subscribe to state changes
    this.stateSubscription = this.appStateService.state$.subscribe(state => {
      this.logEntries = state.logEntries;
    });

    // Get initial state
    this.logEntries = this.appStateService.getLogEntries();
  }

  ngOnDestroy(): void {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  async runAnalysis(): Promise<void> {
    if (this.logEntries.length === 0) {
      this.errorMessage = 'No log entries available for analysis';
      return;
    }

    this.isAnalyzing = true;
    this.errorMessage = '';
    this.analysisResult = null;

    try {
      this.analysisResult = await this.aiService.analyzeLogs(
        this.logEntries,
        this.selectedAnalysisType as any,
        this.context
      );
    } catch (error) {
      this.errorMessage = 'Error during analysis: ' + (error as Error).message;
    } finally {
      this.isAnalyzing = false;
    }
  }

  canRunAnalysis(): boolean {
    return this.logEntries.length > 0 && !this.isAnalyzing;
  }

  getAnalysisTypeIcon(type: string): string {
    const analysisType = this.analysisTypes.find(t => t.value === type);
    return analysisType?.icon || '🤖';
  }

  getAnalysisTypeLabel(type: string): string {
    const analysisType = this.analysisTypes.find(t => t.value === type);
    return analysisType?.label || 'Analysis';
  }

  getInsightIcon(type: string): string {
    const icons: Record<string, string> = {
      'error_pattern': '🔴',
      'performance_issue': '⚡',
      'security_concern': '🔒',
      'trend': '📈',
      'anomaly': '⚠️'
    };
    return icons[type] || '💡';
  }

  getSeverityClass(severity: string): string {
    switch (severity) {
      case 'critical': return 'severity-critical';
      case 'high': return 'severity-high';
      case 'medium': return 'severity-medium';
      case 'low': return 'severity-low';
      default: return 'severity-low';
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'critical': return 'priority-critical';
      case 'high': return 'priority-high';
      case 'medium': return 'priority-medium';
      case 'low': return 'priority-low';
      default: return 'priority-low';
    }
  }

  formatConfidence(confidence: number): string {
    return `${(confidence * 100).toFixed(1)}%`;
  }

  formatProcessingTime(time: number): string {
    return `${(time / 1000).toFixed(1)}s`;
  }
} 