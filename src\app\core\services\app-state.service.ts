import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LogEntry } from '../models/log-entry.model';
import { LogStatistics } from '../models/log-summary.model';

export interface AppState {
  logEntries: LogEntry[];
  logStatistics: LogStatistics | null;
}

@Injectable({
  providedIn: 'root'
})
export class AppStateService {
  private stateSubject = new BehaviorSubject<AppState>({
    logEntries: [],
    logStatistics: null
  });

  public state$: Observable<AppState> = this.stateSubject.asObservable();

  constructor() { }

  // Get current state
  getState(): AppState {
    return this.stateSubject.value;
  }

  // Update log data
  updateLogData(entries: LogEntry[], statistics: LogStatistics): void {
    this.stateSubject.next({
      logEntries: entries,
      logStatistics: statistics
    });
  }

  // Clear log data
  clearLogData(): void {
    this.stateSubject.next({
      logEntries: [],
      logStatistics: null
    });
  }

  // Get log entries
  getLogEntries(): LogEntry[] {
    return this.stateSubject.value.logEntries;
  }

  // Get log statistics
  getLogStatistics(): LogStatistics | null {
    return this.stateSubject.value.logStatistics;
  }

  // Check if log data exists
  hasLogData(): boolean {
    return this.stateSubject.value.logEntries.length > 0;
  }

  // Get log count
  getLogCount(): number {
    return this.stateSubject.value.logEntries.length;
  }

  // Get error count
  getErrorCount(): number {
    return this.stateSubject.value.logEntries.filter(entry => 
      entry.level === 'ERROR' || entry.level === 'FATAL'
    ).length;
  }
} 