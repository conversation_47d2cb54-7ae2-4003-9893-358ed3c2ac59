import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'logLevel',
  standalone: true
})
export class LogLevelPipe implements PipeTransform {

  transform(level: string, format: 'icon' | 'color' | 'class' = 'icon'): string {
    const levelUpper = level.toUpperCase();
    
    switch (format) {
      case 'icon':
        return this.getLevelIcon(levelUpper);
      case 'color':
        return this.getLevelColor(levelUpper);
      case 'class':
        return this.getLevelClass(levelUpper);
      default:
        return levelUpper;
    }
  }

  private getLevelIcon(level: string): string {
    switch (level) {
      case 'ERROR':
      case 'FATAL':
        return '🔴';
      case 'WARN':
        return '🟡';
      case 'INFO':
        return '🔵';
      case 'DEBUG':
        return '⚪';
      default:
        return '⚪';
    }
  }

  private getLevelColor(level: string): string {
    switch (level) {
      case 'ERROR':
      case 'FATAL':
        return '#e74c3c';
      case 'WARN':
        return '#f39c12';
      case 'INFO':
        return '#3498db';
      case 'DEBUG':
        return '#95a5a6';
      default:
        return '#7f8c8d';
    }
  }

  private getLevelClass(level: string): string {
    return `log-level-${level.toLowerCase()}`;
  }
} 