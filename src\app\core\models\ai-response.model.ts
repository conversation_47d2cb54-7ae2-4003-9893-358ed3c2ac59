export interface AIAnalysisRequest {
  logEntries: string[];
  analysisType: 'error_patterns' | 'performance_issues' | 'security_concerns' | 'general_insights';
  context?: string;
}

export interface AIAnalysisResponse {
  id: string;
  timestamp: Date;
  analysisType: string;
  summary: string;
  insights: AIInsight[];
  recommendations: AIRecommendation[];
  confidence: number;
  processingTime: number;
}

export interface AIInsight {
  id: string;
  type: 'error_pattern' | 'performance_issue' | 'security_concern' | 'trend' | 'anomaly';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedEntries: number;
  examples: string[];
}

export interface AIRecommendation {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  action: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
} 