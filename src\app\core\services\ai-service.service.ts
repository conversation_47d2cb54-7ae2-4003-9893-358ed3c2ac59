import { Injectable } from '@angular/core';
import { AIAnalysisRequest, AIAnalysisResponse, AIInsight, AIRecommendation } from '../models/ai-response.model';
import { LogEntry } from '../models/log-entry.model';
import { OpenAIService, OpenAIRequest } from './openai.service';
import { firstValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AIServiceService {

  constructor(private openaiService: OpenAIService) { }

  async analyzeLogs(entries: LogEntry[], analysisType: AIAnalysisRequest['analysisType'], context?: string): Promise<AIAnalysisResponse> {
    const startTime = Date.now();
    
    // Convert log entries to strings for AI processing
    const logStrings = entries.map(entry => 
      `${entry.timestamp.toISOString()} [${entry.level}] ${entry.message}`
    );

    try {
      // Try to use real OpenAI API first
      if (this.openaiService.isConfigured()) {
        return await this.useRealAI(logStrings, analysisType, context, startTime);
      } else {
        // Fallback to simulated AI
        return await this.useSimulatedAI(entries, analysisType, startTime);
      }
    } catch (error) {
      console.warn('AI service error, falling back to simulated response:', error);
      return await this.useSimulatedAI(entries, analysisType, startTime);
    }
  }

  private async useRealAI(logStrings: string[], analysisType: string, context: string | undefined, startTime: number): Promise<AIAnalysisResponse> {
    const request: OpenAIRequest = {
      logEntries: logStrings,
      analysisType,
      context
    };

    // Use firstValueFrom to convert Observable to Promise
    const aiResponse = await firstValueFrom(this.openaiService.analyzeLogs(request));
    const processingTime = Date.now() - startTime;

    return {
      id: this.generateId(),
      timestamp: new Date(),
      analysisType,
      summary: aiResponse.summary,
      insights: aiResponse.insights.map((insight, index) => ({
        id: `ai_insight_${index}`,
        type: insight.type as any,
        title: insight.title,
        description: insight.description,
        severity: insight.severity,
        affectedEntries: insight.affectedEntries,
        examples: insight.examples
      })),
      recommendations: aiResponse.recommendations.map((rec, index) => ({
        id: `ai_rec_${index}`,
        title: rec.title,
        description: rec.description,
        priority: rec.priority,
        action: rec.action,
        impact: rec.impact,
        effort: rec.effort
      })),
      confidence: aiResponse.confidence,
      processingTime
    };
  }

  private async useSimulatedAI(entries: LogEntry[], analysisType: string, startTime: number): Promise<AIAnalysisResponse> {
    // Simulate AI processing delay
    await this.simulateProcessingDelay();

    const insights = this.generateInsights(entries, analysisType);
    const recommendations = this.generateRecommendations(entries, analysisType);
    
    const processingTime = Date.now() - startTime;

    return {
      id: this.generateId(),
      timestamp: new Date(),
      analysisType,
      summary: this.generateSummary(entries, analysisType),
      insights,
      recommendations,
      confidence: this.calculateConfidence(entries, analysisType),
      processingTime
    };
  }

  private async simulateProcessingDelay(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 2000));
  }

  private generateId(): string {
    return `ai_analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSummary(entries: LogEntry[], analysisType: string): string {
    const errorCount = entries.filter(e => e.level === 'ERROR' || e.level === 'FATAL').length;
    const warningCount = entries.filter(e => e.level === 'WARN').length;
    const totalEntries = entries.length;

    switch (analysisType) {
      case 'error_patterns':
        return `Analysis of ${totalEntries} log entries revealed ${errorCount} errors and ${warningCount} warnings. Key error patterns and their frequencies have been identified.`;
      case 'performance_issues':
        return `Performance analysis of ${totalEntries} entries identified potential bottlenecks and performance degradation patterns.`;
      case 'security_concerns':
        return `Security analysis detected ${errorCount} potential security-related issues across ${totalEntries} log entries.`;
      case 'general_insights':
        return `General analysis of ${totalEntries} log entries provides insights into system behavior, error patterns, and operational trends.`;
      default:
        return `Analysis completed for ${totalEntries} log entries.`;
    }
  }

  private generateInsights(entries: LogEntry[], analysisType: string): AIInsight[] {
    const insights: AIInsight[] = [];
    
    if (analysisType === 'error_patterns' || analysisType === 'general_insights') {
      const errorEntries = entries.filter(e => e.level === 'ERROR' || e.level === 'FATAL');
      const errorPatterns = this.findErrorPatterns(errorEntries);
      
      errorPatterns.forEach((pattern, index) => {
        insights.push({
          id: `insight_${index}`,
          type: 'error_pattern',
          title: `Frequent Error: ${pattern.message.substring(0, 50)}...`,
          description: `This error occurred ${pattern.count} times, indicating a recurring issue that needs attention.`,
          severity: pattern.count > 10 ? 'high' : pattern.count > 5 ? 'medium' : 'low',
          affectedEntries: pattern.count,
          examples: pattern.examples.slice(0, 3)
        });
      });
    }

    if (analysisType === 'performance_issues') {
      const performanceInsights = this.analyzePerformance(entries);
      insights.push(...performanceInsights);
    }

    if (analysisType === 'security_concerns') {
      const securityInsights = this.analyzeSecurity(entries);
      insights.push(...securityInsights);
    }

    return insights.slice(0, 5); // Limit to top 5 insights
  }

  private generateRecommendations(entries: LogEntry[], analysisType: string): AIRecommendation[] {
    const recommendations: AIRecommendation[] = [];
    
    const errorCount = entries.filter(e => e.level === 'ERROR' || e.level === 'FATAL').length;
    const totalEntries = entries.length;
    const errorRate = totalEntries > 0 ? (errorCount / totalEntries) * 100 : 0;

    if (errorRate > 10) {
      recommendations.push({
        id: 'rec_1',
        title: 'High Error Rate Detected',
        description: `Error rate is ${errorRate.toFixed(1)}%, which is above recommended thresholds.`,
        priority: 'high',
        action: 'Review error patterns and implement fixes for recurring issues.',
        impact: 'Reduced system reliability and user experience.',
        effort: 'medium'
      });
    }

    if (analysisType === 'performance_issues') {
      recommendations.push({
        id: 'rec_2',
        title: 'Performance Monitoring',
        description: 'Implement comprehensive performance monitoring and alerting.',
        priority: 'medium',
        action: 'Set up performance metrics collection and monitoring dashboards.',
        impact: 'Better visibility into system performance and faster issue resolution.',
        effort: 'high'
      });
    }

    if (analysisType === 'security_concerns') {
      recommendations.push({
        id: 'rec_3',
        title: 'Security Audit',
        description: 'Conduct a comprehensive security audit of the system.',
        priority: 'critical',
        action: 'Review access logs, authentication mechanisms, and security policies.',
        impact: 'Prevent potential security breaches and data leaks.',
        effort: 'high'
      });
    }

    return recommendations;
  }

  private findErrorPatterns(errorEntries: LogEntry[]): Array<{message: string, count: number, examples: string[]}> {
    const patterns: Record<string, {count: number, examples: string[]}> = {};
    
    errorEntries.forEach(entry => {
      const key = entry.message.substring(0, 100);
      if (!patterns[key]) {
        patterns[key] = { count: 0, examples: [] };
      }
      patterns[key].count++;
      patterns[key].examples.push(entry.message);
    });

    return Object.entries(patterns)
      .map(([message, data]) => ({ message, count: data.count, examples: data.examples }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  private analyzePerformance(entries: LogEntry[]): AIInsight[] {
    const insights: AIInsight[] = [];
    
    // Analyze response times if available
    const responseTimeEntries = entries.filter(e => 
      e.message.toLowerCase().includes('response time') || 
      e.message.toLowerCase().includes('duration')
    );

    if (responseTimeEntries.length > 0) {
      insights.push({
        id: 'perf_1',
        type: 'performance_issue',
        title: 'Response Time Analysis',
        description: `Found ${responseTimeEntries.length} entries related to response times. Consider monitoring performance metrics.`,
        severity: 'medium',
        affectedEntries: responseTimeEntries.length,
        examples: responseTimeEntries.slice(0, 3).map(e => e.message)
      });
    }

    return insights;
  }

  private analyzeSecurity(entries: LogEntry[]): AIInsight[] {
    const insights: AIInsight[] = [];
    
    const securityEntries = entries.filter(e => 
      e.message.toLowerCase().includes('authentication') ||
      e.message.toLowerCase().includes('authorization') ||
      e.message.toLowerCase().includes('access denied') ||
      e.message.toLowerCase().includes('security')
    );

    if (securityEntries.length > 0) {
      insights.push({
        id: 'sec_1',
        type: 'security_concern',
        title: 'Security-Related Events',
        description: `Detected ${securityEntries.length} security-related log entries that require attention.`,
        severity: 'high',
        affectedEntries: securityEntries.length,
        examples: securityEntries.slice(0, 3).map(e => e.message)
      });
    }

    return insights;
  }

  private calculateConfidence(entries: LogEntry[], analysisType: string): number {
    // Simple confidence calculation based on data quality and analysis type
    let confidence = 0.7; // Base confidence
    
    if (entries.length > 1000) confidence += 0.1;
    if (entries.length > 100) confidence += 0.1;
    
    const errorEntries = entries.filter(e => e.level === 'ERROR' || e.level === 'FATAL');
    if (errorEntries.length > 0) confidence += 0.1;
    
    return Math.min(confidence, 0.95);
  }

  // Utility methods for monitoring
  getAIServiceStatus(): { configured: boolean; requestCount: number; estimatedCost: number } {
    return {
      configured: this.openaiService.isConfigured(),
      requestCount: 0,
      estimatedCost: 0
    };
  }
} 