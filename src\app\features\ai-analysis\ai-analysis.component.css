.ai-analysis-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.analysis-header {
  text-align: center;
  margin-bottom: 30px;
}

.analysis-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2rem;
}

.analysis-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

.api-config-section {
  margin-bottom: 30px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
}

.analysis-controls {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.control-group select,
.control-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.control-group select:focus,
.control-group textarea:focus {
  outline: none;
  border-color: #3498db;
}

.control-group select:disabled,
.control-group textarea:disabled {
  background-color: #f8f9fa;
  color: #7f8c8d;
}

.analyze-btn {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.analyze-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.analyze-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  background: #fadbd8;
  color: #e74c3c;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid #f1948a;
}

.analysis-results {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-summary {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #ecf0f1;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.summary-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.summary-meta {
  display: flex;
  gap: 16px;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.confidence {
  color: #27ae60;
  font-weight: 600;
}

.processing-time {
  color: #3498db;
  font-weight: 600;
}

.summary-text {
  color: #34495e;
  line-height: 1.6;
  margin: 0;
  font-size: 1.1rem;
}

.insights-section,
.recommendations-section {
  margin-bottom: 30px;
}

.insights-section h3,
.recommendations-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.insights-grid,
.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.insight-card,
.recommendation-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #3498db;
  transition: transform 0.2s ease;
}

.insight-card:hover,
.recommendation-card:hover {
  transform: translateY(-2px);
}

.insight-header,
.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.insight-icon {
  font-size: 1.2rem;
}

.insight-title,
.recommendation-title {
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.severity-badge,
.priority-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.severity-critical,
.priority-critical {
  background: #e74c3c;
  color: white;
}

.severity-high,
.priority-high {
  background: #f39c12;
  color: white;
}

.severity-medium,
.priority-medium {
  background: #f1c40f;
  color: #2c3e50;
}

.severity-low,
.priority-low {
  background: #27ae60;
  color: white;
}

.insight-description,
.recommendation-description {
  color: #34495e;
  line-height: 1.5;
  margin-bottom: 12px;
}

.insight-meta {
  margin-bottom: 12px;
}

.affected-entries {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.insight-examples h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.insight-examples ul {
  margin: 0;
  padding-left: 20px;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.insight-examples li {
  margin-bottom: 4px;
}

.recommendation-details {
  background: #ecf0f1;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

.detail-item {
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item strong {
  color: #2c3e50;
}

.no-data-message {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-data-content h3 {
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 1.5rem;
}

.no-data-content p {
  color: #7f8c8d;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.upload-link {
  display: inline-block;
  padding: 12px 24px;
  background: #3498db;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.upload-link:hover {
  background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-analysis-container {
    padding: 16px;
  }
  
  .summary-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .summary-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .insights-grid,
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
  
  .insight-header,
  .recommendation-header {
    flex-direction: column;
    align-items: flex-start;
  }
} 