.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.logo h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.logo p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.9;
}

.main-nav {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.nav-link:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.3);
  font-weight: 600;
}

.nav-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-link.disabled:hover {
  transform: none;
  background: none;
}

.status-indicator {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.app-main {
  flex: 1;
  background: #f8f9fa;
  min-height: calc(100vh - 140px);
}

.app-footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 1rem;
  font-size: 0.85rem;
}

.app-footer p {
  margin: 0;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .main-nav {
    justify-content: center;
  }
  
  .nav-link {
    flex: 1;
    justify-content: center;
    text-align: center;
  }
  
  .status-indicator {
    text-align: center;
  }
  
  .logo {
    text-align: center;
  }
}
