.log-viewer-container {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.viewer-header {
  margin-bottom: 2rem;
}

.viewer-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.viewer-header p {
  color: #7f8c8d;
  font-size: 0.95rem;
}

.filters-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.filter-group input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.level-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.level-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.level-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.level-btn.selected {
  font-weight: 600;
}

.level-btn.log-level-error {
  border-color: #e74c3c;
  color: #e74c3c;
}

.level-btn.log-level-error.selected {
  background: #e74c3c;
  color: white;
}

.level-btn.log-level-warn {
  border-color: #f39c12;
  color: #f39c12;
}

.level-btn.log-level-warn.selected {
  background: #f39c12;
  color: white;
}

.level-btn.log-level-info {
  border-color: #3498db;
  color: #3498db;
}

.level-btn.log-level-info.selected {
  background: #3498db;
  color: white;
}

.level-btn.log-level-debug {
  border-color: #95a5a6;
  color: #95a5a6;
}

.level-btn.log-level-debug.selected {
  background: #95a5a6;
  color: white;
}

.level-btn.log-level-fatal {
  border-color: #8e44ad;
  color: #8e44ad;
}

.level-btn.log-level-fatal.selected {
  background: #8e44ad;
  color: white;
}

.clear-filters-btn {
  padding: 0.5rem 1rem;
  background: #ecf0f1;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s ease;
}

.clear-filters-btn:hover {
  background: #d5dbdb;
}

.log-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.log-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.log-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
}

.log-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background 0.2s ease;
}

.log-table th.sortable:hover {
  background: #e9ecef;
}

.sort-indicator {
  margin-left: 0.5rem;
  font-weight: bold;
}

.log-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: top;
}

.log-table tbody tr:hover {
  background: #f8f9fa;
}

.log-table tr.log-level-error {
  background: #fdf2f2;
}

.log-table tr.log-level-error:hover {
  background: #fce4e4;
}

.log-table tr.log-level-warn {
  background: #fef9e7;
}

.log-table tr.log-level-warn:hover {
  background: #fef5e7;
}

.log-table tr.log-level-fatal {
  background: #f4e6f7;
}

.log-table tr.log-level-fatal:hover {
  background: #f0e6f7;
}

.timestamp {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #7f8c8d;
  white-space: nowrap;
}

.level {
  width: 100px;
}

.level-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.log-level-error .level-badge {
  background: #e74c3c;
  color: white;
}

.log-level-warn .level-badge {
  background: #f39c12;
  color: white;
}

.log-level-info .level-badge {
  background: #3498db;
  color: white;
}

.log-level-debug .level-badge {
  background: #95a5a6;
  color: white;
}

.log-level-fatal .level-badge {
  background: #8e44ad;
  color: white;
}

.message {
  max-width: 400px;
  word-wrap: break-word;
  line-height: 1.4;
}

.source {
  color: #7f8c8d;
  font-size: 0.85rem;
  font-style: italic;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.pagination-info {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
}

.page-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #3498db;
}

.page-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.page-btn:disabled {
  background: #ecf0f1;
  color: #bdc3c7;
  cursor: not-allowed;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.empty-state p {
  font-size: 0.95rem;
}

@media (max-width: 768px) {
  .filters-section {
    grid-template-columns: 1fr;
  }
  
  .log-table {
    font-size: 0.8rem;
  }
  
  .log-table th,
  .log-table td {
    padding: 0.5rem;
  }
  
  .message {
    max-width: 200px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 1rem;
  }
} 