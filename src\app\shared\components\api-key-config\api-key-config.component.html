<div class="api-key-config-container">
  <div class="config-header">
    <h3>🔑 OpenAI API Configuration</h3>
    <p>Configure your OpenAI API key to enable real AI-powered log analysis.</p>
  </div>

  <!-- Status Display -->
  <div class="status-section">
    <div class="status-item">
      <span class="status-icon">{{ getStatusIcon() }}</span>
      <span class="status-text">{{ getStatusText() }}</span>
    </div>
  </div>

  <!-- API Key Input -->
  <div class="input-section">
    <label for="api-key">OpenAI API Key:</label>
    <div class="input-group">
      <input
        type="password"
        id="api-key"
        [(ngModel)]="apiKey"
        placeholder="sk-..."
        [class.visible]="showKey"
        [type]="showKey ? 'text' : 'password'"
        [disabled]="isSaving"
      />
      <button 
        type="button" 
        class="toggle-btn"
        (click)="toggleShowKey()"
        [disabled]="isSaving">
        {{ showKey ? '👁️' : '👁️‍🗨️' }}
      </button>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="action-buttons">
    <button 
      class="save-btn"
      (click)="saveApiKey()"
      [disabled]="!apiKey.trim() || isSaving">
      <span *ngIf="!isSaving">💾 Save API Key</span>
      <span *ngIf="isSaving">⏳ Saving...</span>
    </button>
    
    <button 
      class="clear-btn"
      (click)="clearApiKey()"
      [disabled]="isSaving">
      🗑️ Clear
    </button>
  </div>

  <!-- Message Display -->
  <div class="message" *ngIf="message" [class]="'message-' + messageType">
    <span class="message-icon">
      {{ messageType === 'success' ? '✅' : messageType === 'error' ? '❌' : 'ℹ️' }}
    </span>
    {{ message }}
  </div>

  <!-- Instructions -->
  <div class="instructions">
    <h4>📋 How to get your OpenAI API key:</h4>
    <ol>
      <li>Visit <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a></li>
      <li>Sign in or create an account</li>
      <li>Go to API Keys section</li>
      <li>Create a new API key</li>
      <li>Copy the key (starts with "sk-")</li>
      <li>Paste it in the field above</li>
    </ol>
    
    <div class="warning">
      <strong>⚠️ Important:</strong>
      <ul>
        <li>Keep your API key secure and never share it</li>
        <li>Monitor your usage to control costs</li>
        <li>Consider using environment variables in production</li>
      </ul>
    </div>
  </div>
</div> 