export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api',
  aiServiceUrl: 'http://localhost:3000/ai',
  maxFileSize: 50 * 1024 * 1024, // 50MB
  supportedFileTypes: ['.log', '.txt'],
  defaultPageSize: 50,
  enableAnalytics: false,
  // OpenAI Configuration
  openai: {
    apiKey: '********************************************************************************************************************************************************************', // Add your OpenAI API key here
    model: 'gpt-4', // or 'gpt-3.5-turbo' for cost optimization
    maxTokens: 2000,
    temperature: 0.3,
    timeout: 30000, // 30 seconds
    retryAttempts: 3
  }
}; 