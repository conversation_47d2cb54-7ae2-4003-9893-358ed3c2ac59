# AI Log Analyzer

A modern Angular application for intelligent log file analysis with AI-powered insights and recommendations.

## Features

### 📤 Log Upload
- Drag & drop file upload
- Support for .log and .txt files
- Real-time processing with progress indicators
- File validation and error handling

### 📋 Log Viewer
- Advanced filtering by log level, date range, and search terms
- Sortable columns (timestamp, level, message)
- Pagination for large log files
- Color-coded log levels for easy identification

### 📊 Summary Dashboard
- Comprehensive statistics and metrics
- Visual charts for hourly distribution
- Top sources and error patterns
- Performance indicators and trends

### 🤖 AI Analysis
- **Real OpenAI API Integration** with automatic fallback to simulated responses
- Multiple analysis types:
  - Error pattern detection
  - Performance issue identification
  - Security concern analysis
  - General insights
- AI-powered recommendations with priority levels
- Confidence scoring for analysis results
- **Cost monitoring and usage tracking**

## AI Integration

### OpenAI API Support

The application now supports real OpenAI API integration with the following features:

- ✅ **Real AI Analysis**: Direct integration with GPT-4 and GPT-3.5-turbo
- ✅ **Automatic Fallback**: Graceful fallback to simulated responses if API is unavailable
- ✅ **API Key Management**: Secure configuration through UI or environment variables
- ✅ **Rate Limiting**: Built-in rate limiting to prevent API quota exhaustion
- ✅ **Error Handling**: Comprehensive error handling with user-friendly messages
- ✅ **Cost Monitoring**: Real-time cost estimation and usage tracking

### Quick Setup

1. **Get OpenAI API Key**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Configure API Key**: Use the UI configuration or environment variables
3. **Test Integration**: Upload logs and run AI analysis

For detailed setup instructions, see [OpenAI Integration Guide](./OPENAI_INTEGRATION.md).

## Project Structure

```
src/
├── app/
│   ├── core/                    # Core services and models
│   │   ├── models/              # Data models
│   │   │   ├── log-entry.model.ts
│   │   │   ├── log-summary.model.ts
│   │   │   └── ai-response.model.ts
│   │   ├── services/
│   │   │   ├── log-parser.service.ts
│   │   │   ├── summary-generator.service.ts
│   │   │   ├── ai-service.service.ts
│   │   │   └── openai.service.ts      # OpenAI API integration
│   │   └── core.module.ts
│   ├── features/                # Feature modules
│   │   ├── log-upload/          # Log file upload feature
│   │   ├── log-viewer/          # Log visualization feature
│   │   ├── summary-dashboard/   # Summary statistics feature
│   │   └── ai-analysis/         # AI analysis feature
│   ├── shared/                  # Shared components and utilities
│   │   ├── components/
│   │   │   └── api-key-config/  # OpenAI API key configuration
│   │   ├── pipes/
│   │   │   └── log-level.pipe.ts
│   │   └── shared.module.ts
│   ├── app-routing.module.ts    # App routing
│   ├── app.component.html       # Main app component
│   ├── app.component.ts
│   └── app.module.ts            # Root module
├── assets/                      # Static assets
└── environments/                # Environment configs
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- OpenAI API key (optional, for real AI analysis)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd AI-Log-Analyzer
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open your browser and navigate to `http://localhost:4200`

### OpenAI API Setup (Optional)

For real AI analysis capabilities:

1. **Get API Key**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Configure Key**: Use the UI configuration in the AI Analysis page
3. **Test**: Upload logs and run AI analysis

### Building for Production

```bash
npm run build
```

The build artifacts will be stored in the `dist/` directory.

## Usage

1. **Upload Logs**: Navigate to the Upload page and drag & drop your log file or click to browse
2. **View Logs**: Use the Log Viewer to filter, search, and sort through your log entries
3. **Dashboard**: Check the Summary Dashboard for statistics and visualizations
4. **AI Analysis**: Run AI-powered analysis to get insights and recommendations
   - **Simulated Mode**: Works without API key (for testing)
   - **Real AI Mode**: Requires OpenAI API key for actual AI analysis

## Technology Stack

- **Frontend**: Angular 17 (Standalone Components)
- **Styling**: CSS3 with modern design patterns
- **Routing**: Angular Router with lazy loading
- **State Management**: Component-based state with services
- **AI Integration**: 
  - OpenAI API (GPT-4, GPT-3.5-turbo)
  - Simulated AI service (fallback)
  - Cost monitoring and rate limiting

## Architecture

### Core Services
- **LogParserService**: Handles log file parsing and entry extraction
- **SummaryGeneratorService**: Generates statistics and summaries
- **AIServiceService**: Manages AI analysis requests and responses
- **OpenAIService**: Handles real OpenAI API integration

### Models
- **LogEntry**: Represents individual log entries with metadata
- **LogSummary**: Contains aggregated statistics and metrics
- **AIAnalysisResponse**: AI analysis results with insights and recommendations

### Features
Each feature is implemented as a standalone Angular component with its own module structure, following the feature-based architecture pattern.

## AI Analysis Modes

### Simulated Mode (Default)
- No API key required
- Fast response times
- Good for testing and development
- Uses predefined patterns and logic

### Real AI Mode
- Requires OpenAI API key
- Actual AI-powered analysis
- More accurate and contextual insights
- Cost-based usage (monitored in UI)

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Future Enhancements

- **Advanced AI Features**:
  - Streaming AI responses
  - Custom analysis templates
  - Industry-specific prompts
- **Export functionality** (PDF, CSV)
- **Real-time log streaming**
- **Advanced charting and visualizations**
- **User authentication and multi-user support**
- **Cloud storage integration**
- **Custom log format support**
- **Alert system for critical issues**
- **Cost controls and budget management**
- **AI response caching**

## Documentation

- [OpenAI Integration Guide](./OPENAI_INTEGRATION.md) - Detailed setup and configuration
- [API Documentation](./docs/api.md) - Service interfaces and models
- [Deployment Guide](./docs/deployment.md) - Production deployment instructions
