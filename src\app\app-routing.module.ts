import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/upload',
    pathMatch: 'full'
  },
  {
    path: 'upload',
    loadComponent: () => import('./features/log-upload/log-upload.component').then(m => m.LogUploadComponent),
    title: 'Upload Logs'
  },
  {
    path: 'viewer',
    loadComponent: () => import('./features/log-viewer/log-viewer.component').then(m => m.LogViewerComponent),
    title: 'Log Viewer'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/summary-dashboard/summary-dashboard.component').then(m => m.SummaryDashboardComponent),
    title: 'Summary Dashboard'
  },
  {
    path: '**',
    redirectTo: '/upload'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
