<div class="log-upload-container">
  <div class="upload-header">
    <h2>Upload Log File</h2>
    <p>Upload your log file to begin analysis. Supported formats: .log, .txt</p>
  </div>

  <div class="upload-area" 
       (dragover)="onDragOver($event)"
       (drop)="onDrop($event)"
       [class.drag-over]="false"
       [class.has-file]="selectedFile">
    
    <div class="upload-content" *ngIf="!selectedFile">
      <div class="upload-icon">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
          <polyline points="7,10 12,15 17,10"/>
          <line x1="12" y1="15" x2="12" y2="3"/>
        </svg>
      </div>
      <h3>Drag & Drop your log file here</h3>
      <p>or</p>
      <label for="file-input" class="file-input-label">
        <span>Choose File</span>
        <input 
          id="file-input" 
          type="file" 
          accept=".log,.txt"
          (change)="onFileSelected($event)"
          style="display: none;">
      </label>
    </div>

    <div class="file-info" *ngIf="selectedFile">
      <div class="file-icon">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
        </svg>
      </div>
      <div class="file-details">
        <h4>{{ selectedFile.name }}</h4>
        <p>{{ getFileSize() }}</p>
      </div>
      <button class="process-btn" (click)="processLogFile()" [disabled]="isProcessing">
        <span *ngIf="!isProcessing">Process File</span>
        <span *ngIf="isProcessing">Processing...</span>
      </button>
    </div>
  </div>

  <div class="progress-container" *ngIf="isProcessing">
    <div class="progress-bar">
      <div class="progress-fill" [style.width.%]="processingProgress"></div>
    </div>
    <p class="progress-text">{{ processingProgress }}% Complete</p>
  </div>

  <div class="error-message" *ngIf="errorMessage">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="10"/>
      <line x1="15" y1="9" x2="9" y2="15"/>
      <line x1="9" y1="9" x2="15" y2="15"/>
    </svg>
    {{ errorMessage }}
  </div>
</div> 